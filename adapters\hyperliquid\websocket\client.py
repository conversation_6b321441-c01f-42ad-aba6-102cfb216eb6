# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

import asyncio
import json
import time
from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional, Set, Union

import msgspec

from nautilus_trader.adapters.hyperliquid.common.constants import (
    HYPERLIQUID_WS_URL_MAINNET,
    HYPERLIQUID_WS_URL_TESTNET,
)
from nautilus_trader.common.component import LiveClock
from nautilus_trader.common.component import Logger
from nautilus_trader.common.enums import LogColor
from nautilus_trader.core.nautilus_pyo3 import Quota
from nautilus_trader.core.nautilus_pyo3 import WebSocketClient
from nautilus_trader.core.nautilus_pyo3 import WebSocketClientError
from nautilus_trader.core.nautilus_pyo3 import WebSocketConfig


# HyperLiquid WebSocket订阅类型
WS_SUBSCRIPTION_TYPES = {
    "allMids": "所有中间价格",
    "notification": "通知消息",
    "webData2": "用户聚合信息",
    "candle": "K线数据",
    "l2Book": "订单簿数据",
    "trades": "交易数据",
    "orderUpdates": "订单更新",
    "userEvents": "用户事件",
    "userFills": "用户成交",
    "userFundings": "用户资金费用",
    "userNonFundingLedgerUpdates": "用户非资金费用账本更新",
    "activeAssetCtx": "活跃资产上下文",
    "activeAssetData": "活跃资产数据",
    "userTwapSliceFills": "用户TWAP切片成交",
    "userTwapHistory": "用户TWAP历史",
    "bbo": "最佳买卖价",
}

# HyperLiquid WebSocket请求类型
WS_REQUEST_TYPES = {
    "info": "信息请求",
    "action": "操作请求",
}

# 主网和测试网超时设置（毫秒）
# WebSocket连接在60秒内没有消息会自动断开
HYPERLIQUID_WS_TIMEOUT_MS = 60000


class HyperliquidWebSocketClient:
    """
    Hyperliquid WebSocket客户端，用于处理实时数据流。
    
    支持市场数据订阅、用户数据订阅、自动重连和心跳机制，以及通过WebSocket发送POST请求。
    
    Parameters
    ----------
    clock : LiveClock
        时钟实例
    handler : Callable[[bytes], None]
        消息处理回调函数
    handler_reconnect : Optional[Callable[..., Awaitable[None]]]
        重连回调函数
    testnet : bool, default False
        是否使用测试网
    base_url : Optional[str]
        WebSocket基础URL，如果为None则根据testnet参数自动选择
    loop : Optional[asyncio.AbstractEventLoop]
        事件循环
    subscription_rate_limit_per_second : int, default 5
        每秒最大订阅请求数
    ping_interval : int, default 50
        心跳间隔（秒）
    """

    def __init__(
        self,
        clock: LiveClock,
        handler: Callable[[bytes], None],
        handler_reconnect: Optional[Callable[..., asyncio.Awaitable[None]]] = None,
        testnet: bool = False,
        base_url: Optional[str] = None,
        loop: Optional[asyncio.AbstractEventLoop] = None,
        subscription_rate_limit_per_second: int = 5,
        ping_interval: int = 50,
    ) -> None:
        self._clock = clock
        self._log = Logger(type(self).__name__)
        self._handler = handler
        self._handler_reconnect = handler_reconnect
        self._loop = loop or asyncio.get_event_loop()
        self._subscription_rate_limit = subscription_rate_limit_per_second
        self._ping_interval = ping_interval
        
        # 设置WebSocket URL
        if base_url:
            self._base_url = base_url
        else:
            self._base_url = HYPERLIQUID_WS_URL_TESTNET if testnet else HYPERLIQUID_WS_URL_MAINNET
            
        # WebSocket客户端
        self._client: Optional[WebSocketClient] = None
        self._is_connected = False
        self._reconnecting = False
        self._reconnect_task: Optional[asyncio.Task] = None
        self._heartbeat_task: Optional[asyncio.Task] = None
        
        # 订阅管理
        self._subscriptions: Dict[str, Set[str]] = defaultdict(set)  # 类型 -> 参数集合
        self._pending_requests: Dict[int, asyncio.Future] = {}  # 请求ID -> Future
        self._next_request_id = 1
        
        # 消息解码器
        self._decoder = msgspec.json.Decoder()
        
    @property
    def is_connected(self) -> bool:
        """
        返回是否已连接
        
        Returns
        -------
        bool
        """
        return self._is_connected and self._client is not None and self._client.is_active()
    
    @property
    def base_url(self) -> str:
        """
        返回WebSocket基础URL
        
        Returns
        -------
        str
        """
        return self._base_url
    
    @property
    def subscriptions(self) -> Dict[str, Set[str]]:
        """
        返回当前活跃的订阅
        
        Returns
        -------
        Dict[str, Set[str]]
        """
        return self._subscriptions
    
    def has_subscription(self, subscription_type: str, params_str: str) -> bool:
        """
        检查是否已订阅特定类型和参数的数据流
        
        Parameters
        ----------
        subscription_type : str
            订阅类型
        params_str : str
            参数JSON字符串
            
        Returns
        -------
        bool
        """
        return params_str in self._subscriptions.get(subscription_type, set())
    
    async def connect(self) -> None:
        """
        连接到WebSocket服务器
        """
        if self.is_connected:
            self._log.warning("已经连接到WebSocket服务器")
            return
            
        self._log.debug(f"正在连接到 {self._base_url}")
        
        config = WebSocketConfig(
            url=self._base_url,
            handler=self._handle_message,
            heartbeat=self._ping_interval // 2,  # 心跳检查间隔为心跳发送间隔的一半
            heartbeat_msg=json.dumps({"method": "ping"}),
            headers=[],
        )
        
        try:
            self._client = await WebSocketClient.connect(
                config=config,
                post_reconnection=self.reconnect,
                default_quota=Quota.rate_per_second(self._subscription_rate_limit),
            )
            
            self._is_connected = True
            self._log.info(f"已连接到 {self._base_url}", LogColor.GREEN)
            
            # 启动心跳任务
            self._start_heartbeat()
            
            # 重新订阅
            await self._resubscribe_all()
        except Exception as e:
            self._log.error(f"WebSocket连接失败: {e}")
            self._is_connected = False
            self._schedule_reconnect()
            
    async def get_connection_status(self) -> Dict[str, Any]:
        """
        获取连接状态信息
        
        Returns
        -------
        Dict[str, Any]
            连接状态信息，包括是否连接、基础URL、活跃订阅数量等
        """
        subscription_counts = {sub_type: len(params) for sub_type, params in self._subscriptions.items()}
        total_subscriptions = sum(len(params) for params in self._subscriptions.values())
        
        return {
            "connected": self.is_connected,
            "base_url": self._base_url,
            "subscription_types": list(self._subscriptions.keys()),
            "subscription_counts_by_type": subscription_counts,
            "total_subscriptions": total_subscriptions,
            "pending_requests": len(self._pending_requests),
        }
        
    async def disconnect(self) -> None:
        """
        断开与WebSocket服务器的连接
        """
        if not self.is_connected or self._client is None:
            self._log.warning("未连接到WebSocket服务器，无法断开连接")
            return
            
        # 停止心跳任务
        if self._heartbeat_task is not None:
            self._heartbeat_task.cancel()
            self._heartbeat_task = None
            
        # 停止重连任务
        if self._reconnect_task is not None:
            self._reconnect_task.cancel()
            self._reconnect_task = None
            
        try:
            await self._client.disconnect()
            self._is_connected = False
            self._client = None
            self._log.info(f"已断开与 {self._base_url} 的连接", LogColor.GREEN)
        except WebSocketClientError as e:
            self._log.error(f"断开WebSocket连接失败: {e}")
    
    def reconnect(self) -> None:
        """
        重新连接到WebSocket服务器
        """
        if self._reconnecting:
            return
            
        self._log.warning(f"正在重新连接到 {self._base_url}", LogColor.YELLOW)
        self._reconnecting = True
        self._schedule_reconnect()
    
    def _schedule_reconnect(self) -> None:
        """
        安排重连任务
        """
        if self._reconnect_task is None or self._reconnect_task.done():
            self._reconnect_task = self._loop.create_task(self._delayed_reconnect())
    
    async def _delayed_reconnect(self) -> None:
        """
        延迟重连，使用指数退避策略
        """
        retry_count = 0
        max_retries = 10
        base_delay = 1.0  # 基础延迟（秒）
        max_delay = 60.0  # 最大延迟（秒）
        
        while retry_count < max_retries and not self.is_connected:
            try:
                # 计算指数退避延迟
                delay = min(base_delay * (2 ** retry_count), max_delay)
                self._log.info(f"等待 {delay:.1f} 秒后尝试重新连接 (尝试 {retry_count + 1}/{max_retries})")
                await asyncio.sleep(delay)
                
                # 尝试重新连接
                await self.connect()
                
                # 如果连接成功，调用重连回调
                if self.is_connected and self._handler_reconnect:
                    await self._handler_reconnect()
                    self._log.info(f"已成功重新连接到 {self._base_url}", LogColor.GREEN)
                    break
                    
            except Exception as e:
                self._log.error(f"重连失败: {e}")
                retry_count += 1
                
        if not self.is_connected:
            self._log.error(f"重连失败次数达到上限 ({max_retries}次)，放弃重连")
            
        self._reconnecting = False
    
    def _start_heartbeat(self) -> None:
        """
        启动心跳任务
        """
        if self._heartbeat_task is not None:
            self._heartbeat_task.cancel()
            
        self._heartbeat_task = self._loop.create_task(self._heartbeat_loop())
    
    async def _heartbeat_loop(self) -> None:
        """
        心跳循环
        """
        while self.is_connected:
            try:
                await asyncio.sleep(self._ping_interval)  # 使用配置的心跳间隔
                await self._send({"method": "ping"})
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"发送心跳失败: {e}")
    
    def _handle_message(self, raw: bytes) -> None:
        """
        处理WebSocket消息
        
        Parameters
        ----------
        raw : bytes
            原始WebSocket消息
        """
        try:
            message = json.loads(raw.decode("utf-8"))
            
            # 处理心跳响应
            if message.get("channel") == "pong":
                self._log.debug("收到心跳响应")
                return
                
            # 处理订阅响应
            if message.get("channel") == "subscriptionResponse":
                self._log.debug(f"订阅确认: {message}")
                return
                
            # 处理POST响应
            if message.get("channel") == "post":
                self._handle_post_response(message)
                return
                
            # 处理其他消息
            self._handler(raw)
        except Exception as e:
            self._log.error(f"处理WebSocket消息失败: {e}")
    
    def _handle_post_response(self, message: Dict[str, Any]) -> None:
        """
        处理POST响应
        
        Parameters
        ----------
        message : Dict[str, Any]
            POST响应消息
        """
        data = message.get("data", {})
        request_id = data.get("id")
        
        if request_id is not None and request_id in self._pending_requests:
            future = self._pending_requests.pop(request_id)
            future.set_result(data.get("response"))
    
    async def _send(self, message: Dict[str, Any]) -> None:
        """
        发送WebSocket消息
        
        Parameters
        ----------
        message : Dict[str, Any]
            要发送的消息
        """
        if not self.is_connected or self._client is None:
            self._log.warning("未连接到WebSocket服务器，无法发送消息")
            return
            
        try:
            message_json = json.dumps(message)
            self._log.debug(f"发送: {message_json}")
            await self._client.send(message_json.encode())
        except WebSocketClientError as e:
            self._log.error(f"发送消息失败: {e}")
            self._schedule_reconnect()
    
    async def subscribe(self, subscription_type: str, **params) -> None:
        """
        订阅特定类型的数据流
        
        Parameters
        ----------
        subscription_type : str
            订阅类型，如 "trades", "l2Book", "candle" 等
        **params
            订阅参数，如 coin="BTC", interval="1m" 等
        """
        # 构建订阅消息
        subscription = {
            "method": "subscribe",
            "subscription": {
                "type": subscription_type,
                **params
            }
        }
        
        # 将参数转换为JSON字符串作为唯一标识
        params_str = json.dumps(params, sort_keys=True)
        
        # 检查是否已订阅
        if self.has_subscription(subscription_type, params_str):
            self._log.warning(f"已订阅 {subscription_type}: {params}")
            return
            
        # 记录订阅
        self._subscriptions[subscription_type].add(params_str)
        
        # 发送订阅请求
        await self._send(subscription)
        self._log.info(f"已订阅 {subscription_type}: {params}", LogColor.BLUE)
    
    async def unsubscribe(self, subscription_type: str, **params) -> None:
        """
        取消订阅特定类型的数据流
        
        Parameters
        ----------
        subscription_type : str
            订阅类型，如 "trades", "l2Book", "candle" 等
        **params
            订阅参数，如 coin="BTC", interval="1m" 等
        """
        # 构建取消订阅消息
        unsubscription = {
            "method": "unsubscribe",
            "subscription": {
                "type": subscription_type,
                **params
            }
        }
        
        # 将参数转换为JSON字符串作为唯一标识
        params_str = json.dumps(params, sort_keys=True)
        
        # 检查是否已订阅
        if not self.has_subscription(subscription_type, params_str):
            self._log.warning(f"未订阅 {subscription_type}: {params}")
            return
            
        # 移除订阅记录
        self._subscriptions[subscription_type].discard(params_str)
        if not self._subscriptions[subscription_type]:
            del self._subscriptions[subscription_type]
            
        # 发送取消订阅请求
        await self._send(unsubscription)
        self._log.info(f"已取消订阅 {subscription_type}: {params}", LogColor.BLUE)
    
    async def _resubscribe_all(self) -> None:
        """
        重新订阅所有订阅，使用限速机制避免请求过于频繁
        """
        if not self._subscriptions:
            return
            
        self._log.info("正在重新订阅所有数据流...")
        
        # 计算总订阅数量和预计完成时间
        total_subscriptions = sum(len(params) for params in self._subscriptions.values())
        delay_per_sub = 1.0 / self._subscription_rate_limit
        estimated_time = total_subscriptions * delay_per_sub
        
        if total_subscriptions > 10:
            self._log.info(f"共有 {total_subscriptions} 个订阅需要恢复，预计需要 {estimated_time:.1f} 秒")
        
        subscription_count = 0
        for subscription_type, params_set in self._subscriptions.items():
            for params_str in params_set:
                params = json.loads(params_str)
                await self.subscribe(subscription_type, **params)
                subscription_count += 1
                
                # 根据限速参数添加适当的延迟
                if subscription_count % self._subscription_rate_limit == 0:
                    await asyncio.sleep(1.0)  # 每发送subscription_rate_limit个请求后等待1秒
                else:
                    await asyncio.sleep(delay_per_sub)  # 否则按照限速添加小延迟
                    
        self._log.info(f"已成功重新订阅 {subscription_count} 个数据流")
        
    async def bulk_subscribe(self, subscriptions: List[Dict[str, Any]]) -> None:
        """
        批量订阅多个数据流，使用限速机制
        
        Parameters
        ----------
        subscriptions : List[Dict[str, Any]]
            订阅列表，每个订阅是一个字典，包含type和其他参数
        """
        if not subscriptions:
            return
            
        self._log.info(f"批量订阅 {len(subscriptions)} 个数据流")
        
        for i, sub in enumerate(subscriptions):
            sub_type = sub.pop("type")
            await self.subscribe(sub_type, **sub)
            
            # 添加适当延迟
            if (i + 1) % self._subscription_rate_limit == 0:
                await asyncio.sleep(1.0)
            else:
                await asyncio.sleep(1.0 / self._subscription_rate_limit)
    
    async def post_request(self, request_type: str, payload: Dict[str, Any], timeout: float = 10.0) -> Dict[str, Any]:
        """
        通过WebSocket发送POST请求
        
        Parameters
        ----------
        request_type : str
            请求类型，"info" 或 "action"
        payload : Dict[str, Any]
            请求负载
        timeout : float, default 10.0
            请求超时时间（秒）
            
        Returns
        -------
        Dict[str, Any]
            响应数据
        
        Raises
        ------
        RuntimeError
            未连接到WebSocket服务器
        TimeoutError
            请求超时
        ValueError
            请求类型无效
        """
        if not self.is_connected:
            raise RuntimeError("未连接到WebSocket服务器，无法发送POST请求")
            
        # 验证请求类型
        if request_type not in ["info", "action"]:
            raise ValueError(f"无效的请求类型: {request_type}, 必须是 'info' 或 'action'")
            
        # 生成请求ID
        request_id = self._next_request_id
        self._next_request_id += 1
        
        # 创建Future
        future = self._loop.create_future()
        self._pending_requests[request_id] = future
        
        # 构建请求消息
        message = {
            "method": "post",
            "id": request_id,
            "request": {
                "type": request_type,
                "payload": payload
            }
        }
        
        # 发送请求
        await self._send(message)
        
        try:
            # 等待响应
            response = await asyncio.wait_for(future, timeout)
            return response
        except asyncio.TimeoutError:
            self._pending_requests.pop(request_id, None)
            raise TimeoutError(f"POST请求超时: {request_type}")
            
    async def post_info_request(self, info_type: str, **params) -> Dict[str, Any]:
        """
        发送信息请求
        
        Parameters
        ----------
        info_type : str
            信息类型，如 "l2Book", "trades", "candle" 等
        **params
            请求参数
            
        Returns
        -------
        Dict[str, Any]
            响应数据
        """
        payload = {"type": info_type, **params}
        return await self.post_request("info", payload)
        
    async def post_action_request(
        self, 
        action: Dict[str, Any], 
        nonce: int, 
        signature: Dict[str, str], 
        vault_address: str
    ) -> Dict[str, Any]:
        """
        发送操作请求（需要签名）
        
        Parameters
        ----------
        action : Dict[str, Any]
            操作对象，包含交易指令等
        nonce : int
            nonce值
        signature : Dict[str, str]
            签名对象，包含r、s、v
        vault_address : str
            金库地址
            
        Returns
        -------
        Dict[str, Any]
            响应数据
        """
        payload = {
            "action": action,
            "nonce": nonce,
            "signature": signature,
            "vaultAddress": vault_address
        }
        return await self.post_request("action", payload)
    
    # 以下是便捷的订阅方法
    
    async def subscribe_trades(self, coin: str) -> None:
        """
        订阅交易数据
        
        Parameters
        ----------
        coin : str
            币种名称
        """
        await self.subscribe("trades", coin=coin)
    
    async def unsubscribe_trades(self, coin: str) -> None:
        """
        取消订阅交易数据
        
        Parameters
        ----------
        coin : str
            币种名称
        """
        await self.unsubscribe("trades", coin=coin)
    
    async def subscribe_order_book(self, coin: str, n_sig_figs: int = 5) -> None:
        """
        订阅订单簿数据
        
        Parameters
        ----------
        coin : str
            币种名称
        n_sig_figs : int, default 5
            有效数字位数
        """
        await self.subscribe("l2Book", coin=coin, nSigFigs=n_sig_figs, mantissa=None)
    
    async def unsubscribe_order_book(self, coin: str) -> None:
        """
        取消订阅订单簿数据
        
        Parameters
        ----------
        coin : str
            币种名称
        """
        await self.unsubscribe("l2Book", coin=coin)
    
    async def subscribe_candles(self, coin: str, interval: str) -> None:
        """
        订阅K线数据
        
        Parameters
        ----------
        coin : str
            币种名称
        interval : str
            K线间隔，如 "1m", "5m", "1h" 等
        """
        await self.subscribe("candle", coin=coin, interval=interval)
    
    async def unsubscribe_candles(self, coin: str, interval: str) -> None:
        """
        取消订阅K线数据
        
        Parameters
        ----------
        coin : str
            币种名称
        interval : str
            K线间隔，如 "1m", "5m", "1h" 等
        """
        await self.unsubscribe("candle", coin=coin, interval=interval)
    
    async def subscribe_bbo(self, coin: str) -> None:
        """
        订阅最佳买卖价
        
        Parameters
        ----------
        coin : str
            币种名称
        """
        await self.subscribe("bbo", coin=coin)
    
    async def unsubscribe_bbo(self, coin: str) -> None:
        """
        取消订阅最佳买卖价
        
        Parameters
        ----------
        coin : str
            币种名称
        """
        await self.unsubscribe("bbo", coin=coin)
    
    async def subscribe_all_mids(self, dex: Optional[str] = None) -> None:
        """
        订阅所有中间价格
        
        Parameters
        ----------
        dex : Optional[str]
            交易所名称
        """
        params = {}
        if dex is not None:
            params["dex"] = dex
        await self.subscribe("allMids", **params)
    
    async def unsubscribe_all_mids(self, dex: Optional[str] = None) -> None:
        """
        取消订阅所有中间价格
        
        Parameters
        ----------
        dex : Optional[str]
            交易所名称
        """
        params = {}
        if dex is not None:
            params["dex"] = dex
        await self.unsubscribe("allMids", **params)
    
    async def subscribe_user_events(self, user: str) -> None:
        """
        订阅用户事件
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.subscribe("userEvents", user=user)
    
    async def unsubscribe_user_events(self, user: str) -> None:
        """
        取消订阅用户事件
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.unsubscribe("userEvents", user=user)
    
    async def subscribe_order_updates(self, user: str) -> None:
        """
        订阅订单更新
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.subscribe("orderUpdates", user=user)
    
    async def unsubscribe_order_updates(self, user: str) -> None:
        """
        取消订阅订单更新
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.unsubscribe("orderUpdates", user=user)
    
    async def subscribe_user_fills(self, user: str, aggregate_by_time: bool = False) -> None:
        """
        订阅用户成交
        
        Parameters
        ----------
        user : str
            用户地址
        aggregate_by_time : bool, default False
            是否按时间聚合
        """
        await self.subscribe("userFills", user=user, aggregateByTime=aggregate_by_time)
    
    async def subscribe_user_fundings(self, user: str) -> None:
        """
        订阅用户资金费用
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.subscribe("userFundings", user=user)
    
    async def subscribe_active_asset_ctx(self, coin: str) -> None:
        """
        订阅活跃资产上下文
        
        Parameters
        ----------
        coin : str
            币种名称
        """
        await self.subscribe("activeAssetCtx", coin=coin)
        
    async def unsubscribe_active_asset_ctx(self, coin: str) -> None:
        """
        取消订阅活跃资产上下文
        
        Parameters
        ----------
        coin : str
            币种名称
        """
        await self.unsubscribe("activeAssetCtx", coin=coin)
        
    async def subscribe_active_asset_data(self, user: str, coin: str) -> None:
        """
        订阅活跃资产数据（仅支持永续合约）
        
        Parameters
        ----------
        user : str
            用户地址
        coin : str
            币种名称
        """
        await self.subscribe("activeAssetData", user=user, coin=coin)
        
    async def unsubscribe_active_asset_data(self, user: str, coin: str) -> None:
        """
        取消订阅活跃资产数据
        
        Parameters
        ----------
        user : str
            用户地址
        coin : str
            币种名称
        """
        await self.unsubscribe("activeAssetData", user=user, coin=coin)
        
    async def subscribe_user_twap_slice_fills(self, user: str) -> None:
        """
        订阅用户TWAP切片成交
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.subscribe("userTwapSliceFills", user=user)
        
    async def unsubscribe_user_twap_slice_fills(self, user: str) -> None:
        """
        取消订阅用户TWAP切片成交
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.unsubscribe("userTwapSliceFills", user=user)
        
    async def subscribe_user_twap_history(self, user: str) -> None:
        """
        订阅用户TWAP历史
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.subscribe("userTwapHistory", user=user)
        
    async def unsubscribe_user_twap_history(self, user: str) -> None:
        """
        取消订阅用户TWAP历史
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.unsubscribe("userTwapHistory", user=user)
        
    async def subscribe_user_non_funding_ledger_updates(self, user: str) -> None:
        """
        订阅用户非资金费用账本更新
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.subscribe("userNonFundingLedgerUpdates", user=user)
        
    async def unsubscribe_user_non_funding_ledger_updates(self, user: str) -> None:
        """
        取消订阅用户非资金费用账本更新
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.unsubscribe("userNonFundingLedgerUpdates", user=user)
        
    async def subscribe_notification(self, user: str) -> None:
        """
        订阅通知消息
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.subscribe("notification", user=user)
        
    async def unsubscribe_notification(self, user: str) -> None:
        """
        取消订阅通知消息
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.unsubscribe("notification", user=user)
        
    async def subscribe_web_data2(self, user: str) -> None:
        """
        订阅用户聚合信息
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.subscribe("webData2", user=user)
        
    async def unsubscribe_web_data2(self, user: str) -> None:
        """
        取消订阅用户聚合信息
        
        Parameters
        ----------
        user : str
            用户地址
        """
        await self.unsubscribe("webData2", user=user)
        
    # 便捷的WebSocket操作方法
    
    async def get_l2_orderbook(self, coin: str, n_sig_figs: int = 5) -> Dict[str, Any]:
        """
        获取L2订单簿快照（通过WebSocket POST请求）
        
        Parameters
        ----------
        coin : str
            币种名称
        n_sig_figs : int, default 5
            有效数字位数
            
        Returns
        -------
        Dict[str, Any]
            订单簿数据
        """
        payload = {
            "type": "l2Book",
            "coin": coin,
            "nSigFigs": n_sig_figs,
            "mantissa": None
        }
        return await self.post_info_request(**payload)
    
    async def get_recent_trades(self, coin: str) -> Dict[str, Any]:
        """
        获取最近成交（通过WebSocket POST请求）
        
        Parameters
        ----------
        coin : str
            币种名称
            
        Returns
        -------
        Dict[str, Any]
            成交数据
        """
        payload = {
            "type": "trades",
            "coin": coin
        }
        return await self.post_info_request(**payload)
    
    async def get_candle_data(self, coin: str, interval: str) -> Dict[str, Any]:
        """
        获取K线数据（通过WebSocket POST请求）
        
        Parameters
        ----------
        coin : str
            币种名称
        interval : str
            K线间隔，如 "1m", "5m", "1h" 等
            
        Returns
        -------
        Dict[str, Any]
            K线数据
        """
        payload = {
            "type": "candle",
            "coin": coin,
            "interval": interval
        }
        return await self.post_info_request(**payload)
    
    async def subscribe_to_all_markets(self, instruments: List[str], data_types: List[str] = None) -> None:
        """
        订阅所有市场的指定数据类型
        
        Parameters
        ----------
        instruments : List[str]
            币种名称列表
        data_types : List[str], default None
            要订阅的数据类型列表，如 ["trades", "l2Book", "bbo"]
            如果为None，则默认订阅 trades 和 l2Book
        """
        if data_types is None:
            data_types = ["trades", "l2Book"]
            
        subscriptions = []
        for instrument in instruments:
            for data_type in data_types:
                if data_type == "trades":
                    subscriptions.append({"type": "trades", "coin": instrument})
                elif data_type == "l2Book":
                    subscriptions.append({"type": "l2Book", "coin": instrument, "nSigFigs": 5, "mantissa": None})
                elif data_type == "bbo":
                    subscriptions.append({"type": "bbo", "coin": instrument})
                elif data_type == "candle":
                    # 默认订阅1分钟K线
                    subscriptions.append({"type": "candle", "coin": instrument, "interval": "1m"})
                elif data_type == "activeAssetCtx":
                    subscriptions.append({"type": "activeAssetCtx", "coin": instrument})
                    
        await self.bulk_subscribe(subscriptions)
        
    async def place_order_via_ws(
        self, 
        action: Dict[str, Any], 
        nonce: int, 
        signature: Dict[str, str], 
        vault_address: str
    ) -> Dict[str, Any]:
        """
        通过WebSocket发送下单请求
        
        Parameters
        ----------
        action : Dict[str, Any]
            订单操作对象
        nonce : int
            nonce值
        signature : Dict[str, str]
            签名对象
        vault_address : str
            金库地址
            
        Returns
        -------
        Dict[str, Any]
            响应数据
        """
        return await self.post_action_request(action, nonce, signature, vault_address)
