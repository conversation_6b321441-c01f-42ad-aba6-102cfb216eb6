["test_signing.py::TestConstants::test_approve_builder_fee_sign_types", "test_signing.py::TestConstants::test_l1_agent_eip712_domain", "test_signing.py::TestConstants::test_l1_agent_message_type", "test_signing.py::TestConstants::test_usd_send_sign_types", "test_signing.py::TestConstants::test_withdraw_sign_types", "test_signing.py::TestFloatToIntForHashing::test_float_to_int_for_hashing_decimal_numbers", "test_signing.py::TestFloatToIntForHashing::test_float_to_int_for_hashing_large_numbers", "test_signing.py::TestFloatToIntForHashing::test_float_to_int_for_hashing_precision_error", "test_signing.py::TestFloatToIntForHashing::test_float_to_int_for_hashing_small_numbers", "test_signing.py::TestFloatToWire::test_float_to_wire_float_conversion", "test_signing.py::TestFloatToWire::test_float_to_wire_negative_zero", "test_signing.py::TestFloatToWire::test_float_to_wire_precision_error", "test_signing.py::TestFloatToWire::test_float_to_wire_string_passthrough", "test_signing.py::TestHyperliquidSigner::test_action_hash_with_vault", "test_signing.py::TestHyperliquidSigner::test_action_hash_without_vault", "test_signing.py::TestHyperliquidSigner::test_address_to_bytes", "test_signing.py::TestHyperliquidSigner::test_address_to_bytes_without_0x", "test_signing.py::TestHyperliquidSigner::test_automatic_nonce_generation", "test_signing.py::TestHyperliquidSigner::test_automatic_time_generation_usd_transfer", "test_signing.py::TestHyperliquidSigner::test_construct_phantom_agent_mainnet", "test_signing.py::TestHyperliquidSigner::test_construct_phantom_agent_testnet", "test_signing.py::TestHyperliquidSigner::test_create_sub_account_action_mainnet", "test_signing.py::TestHyperliquidSigner::test_create_sub_account_action_testnet", "test_signing.py::TestHyperliquidSigner::test_order_type_to_wire_invalid", "test_signing.py::TestHyperliquidSigner::test_order_type_to_wire_limit", "test_signing.py::TestHyperliquidSigner::test_order_type_to_wire_trigger", "test_signing.py::TestHyperliquidSigner::test_phantom_agent_creation_matches_production", "test_signing.py::TestHyperliquidSigner::test_phantom_agent_creation_structure", "test_signing.py::TestHyperliquidSigner::test_schedule_cancel_action_mainnet", "test_signing.py::TestHyperliquidSigner::test_schedule_cancel_action_testnet", "test_signing.py::TestHyperliquidSigner::test_schedule_cancel_action_with_time_mainnet", "test_signing.py::TestHyperliquidSigner::test_schedule_cancel_action_with_time_testnet", "test_signing.py::TestHyperliquidSigner::test_sign_approve_agent", "test_signing.py::TestHyperliquidSigner::test_sign_approve_builder_fee", "test_signing.py::TestHyperliquidSigner::test_sign_cancel_order", "test_signing.py::TestHyperliquidSigner::test_sign_convert_to_multi_sig_user_action", "test_signing.py::TestHyperliquidSigner::test_sign_l1_action_basic_mainnet", "test_signing.py::TestHyperliquidSigner::test_sign_l1_action_basic_testnet", "test_signing.py::TestHyperliquidSigner::test_sign_l1_action_with_vault_mainnet", "test_signing.py::TestHyperliquidSigner::test_sign_l1_action_with_vault_testnet", "test_signing.py::TestHyperliquidSigner::test_sign_order_basic", "test_signing.py::TestHyperliquidSigner::test_sign_order_basic_mainnet", "test_signing.py::TestHyperliquidSigner::test_sign_order_basic_testnet", "test_signing.py::TestHyperliquidSigner::test_sign_order_trigger_type", "test_signing.py::TestHyperliquidSigner::test_sign_order_trigger_type_mainnet", "test_signing.py::TestHyperliquidSigner::test_sign_order_trigger_type_testnet", "test_signing.py::TestHyperliquidSigner::test_sign_order_with_cloid", "test_signing.py::TestHyperliquidSigner::test_sign_order_with_cloid_mainnet", "test_signing.py::TestHyperliquidSigner::test_sign_order_with_cloid_testnet", "test_signing.py::TestHyperliquidSigner::test_sign_perp_dex_class_transfer_action", "test_signing.py::TestHyperliquidSigner::test_sign_token_delegate_action", "test_signing.py::TestHyperliquidSigner::test_sign_update_leverage", "test_signing.py::TestHyperliquidSigner::test_sign_usd_transfer_action_mainnet", "test_signing.py::TestHyperliquidSigner::test_sign_usd_transfer_action_testnet", "test_signing.py::TestHyperliquidSigner::test_sign_user_signed_action_basic", "test_signing.py::TestHyperliquidSigner::test_sign_withdraw", "test_signing.py::TestHyperliquidSigner::test_signer_initialization_with_0x_prefix", "test_signing.py::TestHyperliquidSigner::test_signer_initialization_with_invalid_key", "test_signing.py::TestHyperliquidSigner::test_signer_initialization_without_0x_prefix", "test_signing.py::TestHyperliquidSigner::test_signer_with_vault_address_default", "test_signing.py::TestHyperliquidSigner::test_sub_account_transfer_action_mainnet", "test_signing.py::TestHyperliquidSigner::test_sub_account_transfer_action_testnet", "test_signing_new.py::TestConstants::test_approve_builder_fee_sign_types", "test_signing_new.py::TestConstants::test_l1_agent_eip712_domain", "test_signing_new.py::TestConstants::test_l1_agent_message_type", "test_signing_new.py::TestConstants::test_usd_send_sign_types", "test_signing_new.py::TestConstants::test_withdraw_sign_types", "test_signing_new.py::TestFloatToWire::test_float_to_int_for_hashing", "test_signing_new.py::TestFloatToWire::test_float_to_wire_float_conversion", "test_signing_new.py::TestFloatToWire::test_float_to_wire_negative_zero", "test_signing_new.py::TestFloatToWire::test_float_to_wire_precision_error", "test_signing_new.py::TestFloatToWire::test_float_to_wire_string_passthrough", "test_signing_new.py::TestHyperliquidSigner::test_address_to_bytes", "test_signing_new.py::TestHyperliquidSigner::test_address_to_bytes_without_0x", "test_signing_new.py::TestHyperliquidSigner::test_create_sub_account_action_mainnet", "test_signing_new.py::TestHyperliquidSigner::test_create_sub_account_action_testnet", "test_signing_new.py::TestHyperliquidSigner::test_order_type_to_wire_invalid", "test_signing_new.py::TestHyperliquidSigner::test_order_type_to_wire_limit", "test_signing_new.py::TestHyperliquidSigner::test_order_type_to_wire_trigger", "test_signing_new.py::TestHyperliquidSigner::test_phantom_agent_creation_matches_production", "test_signing_new.py::TestHyperliquidSigner::test_sign_l1_action_basic_mainnet", "test_signing_new.py::TestHyperliquidSigner::test_sign_l1_action_basic_testnet", "test_signing_new.py::TestHyperliquidSigner::test_sign_l1_action_with_vault_mainnet", "test_signing_new.py::TestHyperliquidSigner::test_sign_l1_action_with_vault_testnet", "test_signing_new.py::TestHyperliquidSigner::test_sign_order_basic_mainnet", "test_signing_new.py::TestHyperliquidSigner::test_sign_order_basic_testnet", "test_signing_new.py::TestHyperliquidSigner::test_sign_order_trigger_type_mainnet", "test_signing_new.py::TestHyperliquidSigner::test_sign_order_trigger_type_testnet", "test_signing_new.py::TestHyperliquidSigner::test_sign_order_with_cloid_mainnet", "test_signing_new.py::TestHyperliquidSigner::test_sign_order_with_cloid_testnet", "test_signing_new.py::TestHyperliquidSigner::test_sign_usd_transfer_action_mainnet", "test_signing_new.py::TestHyperliquidSigner::test_sign_withdraw_action_mainnet", "test_signing_new.py::TestHyperliquidSigner::test_signer_initialization_with_0x_prefix", "test_signing_new.py::TestHyperliquidSigner::test_signer_initialization_with_invalid_key", "test_signing_new.py::TestHyperliquidSigner::test_signer_initialization_without_0x_prefix", "test_signing_new.py::TestHyperliquidSigner::test_sub_account_transfer_action_mainnet", "test_signing_new.py::TestHyperliquidSigner::test_sub_account_transfer_action_testnet"]