{"test_signing_new.py::TestFloatToWire::test_float_to_int_for_hashing": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_l1_action_basic_mainnet": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_l1_action_basic_testnet": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_l1_action_with_vault_mainnet": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_l1_action_with_vault_testnet": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_order_basic_mainnet": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_order_basic_testnet": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_order_with_cloid_mainnet": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_order_with_cloid_testnet": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_order_trigger_type_mainnet": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_order_trigger_type_testnet": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_usd_transfer_action_mainnet": true, "test_signing_new.py::TestHyperliquidSigner::test_sign_withdraw_action_mainnet": true, "test_signing.py::TestHyperliquidSigner::test_phantom_agent_creation_matches_production": true}