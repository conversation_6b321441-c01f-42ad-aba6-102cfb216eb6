import hashlib
import json
import threading
from decimal import Decimal
from typing import Any, Dict, List, Literal, Optional, Tuple, TypedDict, Union

import msgspec
from eth_account import Account
from eth_account.messages import encode_typed_data
from eth_utils import keccak
from nautilus_trader.common.component import LiveClock


def float_to_wire(x: Union[str, float]) -> str:
    """
    Converts a float to a string representation for the API, ensuring proper precision.
    If already a string, returns as is.

    Args:
        x: The float value to convert or a string to pass through

    Returns:
        The string representation of the float with proper precision
    """
    if isinstance(x, str):
        return x

    rounded = f"{x:.8f}"
    if abs(float(rounded) - x) >= 1e-12:
        raise ValueError(f"float_to_wire causes rounding: {x}")
    if rounded == "-0":
        rounded = "0"
    normalized = Decimal(rounded).normalize()
    return f"{normalized:f}"


def float_to_int_for_hashing(x: float) -> int:
    """
    Converts a float to an integer for hashing purposes .

    Args:
        x: The float value to convert

    Returns:
        The integer representation for hashing
    """
    return float_to_int(x, 8)


def float_to_int(x: float, power: int) -> int:
    """
    Converts a float to an integer by multiplying by 10^power .

    Args:
        x: The float value to convert
        power: The power of 10 to multiply by

    Returns:
        The integer representation

    Raises:
        ValueError: If the conversion would cause rounding
    """
    with_decimals = x * 10**power
    if abs(round(with_decimals) - with_decimals) >= 1e-3:
        raise ValueError(f"float_to_int causes rounding: {x}")
    res: int = round(with_decimals)
    return res

L1_AGENT_EIP712_DOMAIN: Dict[str, Any] = {
    "name": "Exchange",
    "version": "1",
    "chainId": 1337,
    "verifyingContract": "******************************************",
}

L1_AGENT_MESSAGE_TYPE: Dict[str, Any] = {
    "Agent": [
        {"name": "source", "type": "string"},
        {"name": "connectionId", "type": "bytes32"},
    ]
}

EIP712_DOMAIN_TYPE_SCHEMA: List[Dict[str, str]] = [
    {"name": "name", "type": "string"},
    {"name": "version", "type": "string"},
    {"name": "chainId", "type": "uint256"},
    {"name": "verifyingContract", "type": "address"},
]

# User signed transaction types
USD_SEND_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "destination", "type": "string"},
    {"name": "amount", "type": "string"},
    {"name": "time", "type": "uint64"},
]

SPOT_TRANSFER_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "destination", "type": "string"},
    {"name": "token", "type": "string"},
    {"name": "amount", "type": "string"},
    {"name": "time", "type": "uint64"},
]

WITHDRAW_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "destination", "type": "string"},
    {"name": "amount", "type": "string"},
    {"name": "time", "type": "uint64"},
]

USD_CLASS_TRANSFER_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "amount", "type": "string"},
    {"name": "toPerp", "type": "bool"},
    {"name": "nonce", "type": "uint64"},
]

APPROVE_BUILDER_FEE_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "maxFeeRate", "type": "string"},
    {"name": "builder", "type": "address"},
    {"name": "nonce", "type": "uint64"},
]

PERP_DEX_CLASS_TRANSFER_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "dex", "type": "string"},
    {"name": "token", "type": "string"},
    {"name": "amount", "type": "string"},
    {"name": "toPerp", "type": "bool"},
    {"name": "nonce", "type": "uint64"},
]

TOKEN_DELEGATE_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "validator", "type": "address"},
    {"name": "wei", "type": "uint64"},
    {"name": "isUndelegate", "type": "bool"},
    {"name": "nonce", "type": "uint64"},
]

CONVERT_TO_MULTI_SIG_USER_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "signers", "type": "string"},
    {"name": "nonce", "type": "uint64"},
]

MULTI_SIG_ENVELOPE_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "multiSigActionHash", "type": "bytes32"},
    {"name": "nonce", "type": "uint64"},
]

APPROVE_AGENT_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "agentAddress", "type": "address"},
    {"name": "agentName", "type": "string"},
    {"name": "nonce", "type": "uint64"},
]


class HyperliquidSigner:
    """
    Handles signing of messages according to HyperLiquid's specific EIP-712 and hashing scheme.
    Ref:
        https://github.com/hyperliquid-dex/hyperliquid-python-sdk/blob/a8edca1ea20b6efe8235f4bbd9d6e9096e3aede6/hyperliquid/utils/signing.py
    """
    def __init__(
        self,
        clock: LiveClock,
        private_key_hex: str,
        is_mainnet: bool = True,
        vault_address: Optional[str] = None,
    ):
        """
        Initializes the signer with a private key and network settings.

        Args:
            clock: Optional LiveClock for timestamp generation. If None, system time will be used.
            private_key_hex: The private key as a hexadecimal string (e.g., "0x...").
            is_mainnet: Whether to use mainnet (True) or testnet (False) for signatures.
            vault_address: Optional default vault address for actions that require it.
        """
        self._clock = clock
        if not private_key_hex.startswith("0x"):
            private_key_hex = "0x" + private_key_hex
        try:
            self._account = Account.from_key(private_key_hex)
        except Exception as e:
            raise ValueError(f"Invalid private key: {e}")

        self._is_mainnet = is_mainnet
        self._vault_address = vault_address

    @staticmethod
    def address_to_bytes(address: str) -> bytes:
        """
        Converts an Ethereum address to bytes.

        Args:
            address: Ethereum address as a hex string.

        Returns:
            The address as bytes.
        """
        return bytes.fromhex(address[2:] if address.startswith("0x") else address)

    @staticmethod
    def action_hash(action: Dict[str, Any], vault_address: Optional[str], nonce: int, expires_after: Optional[int] = None) -> bytes:
        """
        Computes the hash of an action for signing.

        Args:
            action: The action to hash.
            vault_address: Optional vault address.
            nonce: The nonce value.
            expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            The hash as bytes.
        """
        data = msgspec.msgpack.encode(action)
        data += nonce.to_bytes(8, "big")
        if vault_address is None:
            data += b"\x00"  # Single null byte
        else:
            data += b"\x01"  # Single byte 0x01
            data += HyperliquidSigner.address_to_bytes(vault_address)

        # Add expiration time if provided
        if expires_after is not None:
            data += b"\x00"  # Marker for expiration time
            data += expires_after.to_bytes(8, "big")

        return keccak(data)

    @staticmethod
    def construct_phantom_agent(hash_bytes: bytes, is_mainnet: bool) -> Dict[str, Any]:
        """
        Constructs a phantom agent message for signing.

        Args:
            hash_bytes: The hash bytes.
            is_mainnet: Whether this is for mainnet.

        Returns:
            The phantom agent message.
        """
        return {"source": "a" if is_mainnet else "b", "connectionId": hash_bytes}

    def sign_inner(self, data: Dict[str, Any]) -> Dict[str, str]:
        """
        Signs EIP-712 structured data.

        Args:
            data: The structured data to sign.

        Returns:
            A dictionary with r, s, v components of the signature.
        """
        structured_data = encode_typed_data(full_message=data)
        signed = self._account.sign_message(structured_data)
        return {"r": hex(signed["r"]), "s": hex(signed["s"]), "v": signed["v"]}

    def sign_l1_action(
        self,
        action: Dict[str, Any],
        vault_address: Optional[str] = None,
        nonce: Optional[int] = None,
        is_mainnet: Optional[bool] = None,
        expires_after: Optional[int] = None
    ) -> Dict[str, str]:
        """
        Signs an L1 action for HyperLiquid.

        Args:
            action: The action to sign.
            vault_address: Optional vault address. Defaults to the signer's vault_address if set.
            nonce: The nonce value (if None, current timestamp will be used).
            is_mainnet: Whether this is for mainnet. Defaults to the signer's is_mainnet setting.
            expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            The structured data to sign.
        """
        # Use instance defaults if not explicitly provided
        if vault_address is None:
            vault_address = self._vault_address

        if is_mainnet is None:
            is_mainnet = self._is_mainnet

        # Generate timestamp if nonce is not provided
        if nonce is None:
            nonce = self._clock.timestamp_ms()

        action_hash = self.action_hash(action, vault_address, nonce, expires_after)
        phantom_agent = self.construct_phantom_agent(action_hash, is_mainnet)

        data = {
            "domain": L1_AGENT_EIP712_DOMAIN,
            "types": {
                "EIP712Domain": EIP712_DOMAIN_TYPE_SCHEMA,
                "Agent": L1_AGENT_MESSAGE_TYPE["Agent"],
            },
            "primaryType": "Agent",
            "message": phantom_agent,
        }

        return self.sign_inner(data)

    def sign_order(
        self,
        order_params: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Signs an order action with dict parameters (similar to sign_cancel_order pattern).

        Args:
            order_params: Dictionary containing order parameters:
                Required:
                - asset: Asset ID (int)
                - is_buy: Whether this is a buy order (bool)
                - sz: Order size (float or str)
                - limit_px: Limit price (float or str)
                - order_type: Order type.
                - reduce_only: Whether this is reduce-only (bool)

                Optional:
                - cloid: Client order ID (str)
                - grouping: Order grouping (str, default: "na")
                - builder: Builder info (dict)
                - vault_address: Vault address (str)
                - nonce: Nonce value (int, if None uses timestamp)
                - is_mainnet: Whether mainnet (bool, defaults to signer's setting)
                - expires_after: Expiration time in ms (int)

        Returns:
            A dictionary with the signed order payload.

        Example order_params:
            {
                "asset": 0,
                "is_buy": True,
                "sz": 0.001,
                "limit_px": 30000.0,
                "order_type": {"limit": {"tif": "Gtc"}},
                "reduce_only": False,
                "cloid": "my_order_123"
            }
        """
        # Extract required parameters
        asset = order_params["asset"]
        is_buy = order_params["is_buy"]
        sz = order_params["sz"]
        limit_px = order_params["limit_px"]
        order_type = order_params["order_type"]
        reduce_only = order_params["reduce_only"]

        # Extract optional parameters with defaults
        cloid = order_params.get("cloid")
        grouping = order_params.get("grouping", "na")
        builder = order_params.get("builder")
        vault_address = order_params.get("vault_address", self._vault_address)
        nonce = order_params.get("nonce")
        is_mainnet = order_params.get("is_mainnet", self._is_mainnet)
        expires_after = order_params.get("expires_after")

        # Generate timestamp if nonce not provided
        if nonce is None:
            nonce = self._clock.timestamp_ms()

        # Convert order to wire format 
        order_wire = {
            "a": asset,
            "b": is_buy,
            "p": float_to_wire(limit_px),  # p = price
            "s": float_to_wire(sz),        # s = size
            "r": reduce_only,
            "t": self._order_type_to_wire(order_type),
        }

        # Add client order ID if provided
        if cloid:
            order_wire["c"] = cloid

        # Build action 
        action = {
            "type": "order",
            "orders": [order_wire],
            "grouping": grouping,
        }

        # Add builder if provided
        if builder:
            action["builder"] = builder

        # Sign the L1 action
        return self.sign_l1_action(
            action=action,
            vault_address=vault_address,
            nonce=nonce,
            is_mainnet=is_mainnet,
            expires_after=expires_after,
        )

    def _order_type_to_wire(self, order_type: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert order type to wire format .

        Args:
            order_type: Order type dict in format:
                {"limit": {"tif": "Gtc"}} or
                {"trigger": {"triggerPx": 30000.0, "isMarket": True, "tpsl": "tp"}}

        Returns:
            Wire format order type dict.
        """
        if "limit" in order_type:
            return {"limit": order_type["limit"]}
        elif "trigger" in order_type:
            trigger = order_type["trigger"]
            return {
                "trigger": {
                    "isMarket": trigger["isMarket"],
                    "triggerPx": float_to_wire(trigger["triggerPx"]),
                    "tpsl": trigger["tpsl"],
                }
            }
        else:
            raise ValueError(f"Invalid order type: {order_type}")

    def sign_cancel_order(
        self,
        client_id: str,
        cancel_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a cancel order action with simplified parameters.

        Args:
            client_id: The client ID of the order to cancel.
            cancel_params: Optional dictionary with additional cancel parameters:
                - vault_address: Optional vault address.
                - nonce: The nonce value (if None, current timestamp will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).
                - expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            A dictionary with the signed cancel order payload.
        """
        # Default parameters
        if cancel_params is None:
            cancel_params = {}

        vault_address = cancel_params.get("vault_address", self._vault_address)
        nonce = cancel_params.get("nonce", None)
        is_mainnet = cancel_params.get("is_mainnet", self._is_mainnet)
        expires_after = cancel_params.get("expires_after", None)

        action = {
            "type": "cancelByCloid",
            "cancels": [client_id],
        }

        return self.sign_l1_action(action, vault_address, nonce, is_mainnet, expires_after)

    def sign_update_leverage(
        self,
        asset_id: int,
        leverage: Union[str, float],
        leverage_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs an update leverage action with simplified parameters.

        Args:
            asset_id: The asset ID.
            leverage: The leverage as a string or float.
            leverage_params: Optional dictionary with additional parameters:
                - vault_address: Optional vault address.
                - nonce: The nonce value (if None, current timestamp will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).
                - expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            A dictionary with the signed update leverage payload.
        """
        # Default parameters
        if leverage_params is None:
            leverage_params = {}

        vault_address = leverage_params.get("vault_address", self._vault_address)
        nonce = leverage_params.get("nonce", None)
        is_mainnet = leverage_params.get("is_mainnet", self._is_mainnet)
        expires_after = leverage_params.get("expires_after", None)

        # Convert leverage to wire format
        leverage_str = float_to_wire(leverage)

        action = {
            "type": "updateLeverage",
            "coin": asset_id,
            "leverage": leverage_str,
        }

        return self.sign_l1_action(action, vault_address, nonce, is_mainnet, expires_after)

    def sign_withdraw(
        self,
        amount: Union[str, float],
        destination: str,
        withdraw_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a withdraw action with simplified parameters.

        Args:
            amount: The amount to withdraw as a string or float.
            destination: The destination address.
            withdraw_params: Optional dictionary with additional parameters:
                - vault_address: Optional vault address.
                - nonce: The nonce value (if None, current timestamp will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).
                - expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            A dictionary with the signed withdraw payload.
        """
        # Default parameters
        if withdraw_params is None:
            withdraw_params = {}

        vault_address = withdraw_params.get("vault_address", self._vault_address)
        nonce = withdraw_params.get("nonce", None)
        is_mainnet = withdraw_params.get("is_mainnet", self._is_mainnet)
        expires_after = withdraw_params.get("expires_after", None)

        # Convert amount to wire format
        amount_str = float_to_wire(amount)

        action = {
            "type": "withdraw",
            "amount": amount_str,
            "destination": destination,
        }

        return self.sign_l1_action(action, vault_address, nonce, is_mainnet, expires_after)

    def sign_user_signed_action(
        self,
        action: Dict[str, Any],
        payload_types: List[Dict[str, str]],
        primary_type: str,
        is_mainnet: Optional[bool] = None,
    ) -> Dict[str, str]:
        """
        Signs a user-signed action for HyperLiquid.

        Args:
            action: The action to sign.
            payload_types: The payload types for the action.
            primary_type: The primary type for the action.
            is_mainnet: Whether this is for mainnet. Defaults to the signer's is_mainnet setting.

        Returns:
            The structured data to sign.
        """
        # Use instance defaults if not explicitly provided
        if is_mainnet is None:
            is_mainnet = self._is_mainnet

        # Add chain information
        action = action.copy()
        action["signatureChainId"] = "0x66eee"  # Can be any chain
        action["hyperliquidChain"] = "Mainnet" if is_mainnet else "Testnet"

        # Create the payload
        data = {
            "domain": {
                "name": "HyperliquidSignTransaction",
                "version": "1",
                "chainId": int(action["signatureChainId"], 16),
                "verifyingContract": "******************************************",
            },
            "types": {
                "EIP712Domain": EIP712_DOMAIN_TYPE_SCHEMA,
                primary_type: payload_types,
            },
            "primaryType": primary_type,
            "message": action,
        }

        return  self.sign_inner(data)

    def sign_usd_transfer_action(
        self,
        destination: str,
        amount: Union[str, float],
        transfer_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a USD transfer action with simplified parameters.

        Args:
            destination: The destination address.
            amount: The amount to transfer as a string or float.
            transfer_params: Optional dictionary with additional parameters:
                - time_ms: The time in milliseconds (if None, current time will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).

        Returns:
            A dictionary with the signature and payload ready to be sent to the API.
        """
        # Default parameters
        if transfer_params is None:
            transfer_params = {}

        is_mainnet = transfer_params.get("is_mainnet", self._is_mainnet)
        time_ms = transfer_params.get("time_ms", None)

        # Generate timestamp if not provided
        if time_ms is None:
                time_ms = self._clock.timestamp_ms()
        # Convert amount to wire format if it's a float
        amount_str = float_to_wire(amount)

        action = {
            "destination": destination,
            "amount": amount_str,
            "time": time_ms,
        }

        return self.sign_user_signed_action(
            action=action,
            payload_types=USD_SEND_SIGN_TYPES,
            primary_type="HyperliquidTransaction:UsdSend",
            is_mainnet=is_mainnet,
        )

    def sign_approve_builder_fee(
        self,
        max_fee_rate: str,
        builder: str,
        fee_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs an approve builder fee action with simplified parameters.

        Args:
            max_fee_rate: The maximum fee rate.
            builder: The builder address.
            fee_params: Optional dictionary with additional parameters:
                - nonce: The nonce (if None, current time will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).

        Returns:
            A dictionary with the signature and payload ready to be sent to the API.
        """
        # Default parameters
        if fee_params is None:
            fee_params = {}

        is_mainnet = fee_params.get("is_mainnet", self._is_mainnet)
        nonce = fee_params.get("nonce", None)

        # Generate timestamp if not provided
        if nonce is None:
            nonce = self._clock.timestamp_ms()

        action = {
            "maxFeeRate": max_fee_rate,
            "builder": builder,
            "nonce": nonce,
        }

        return self.sign_user_signed_action(
            action=action,
            payload_types=APPROVE_BUILDER_FEE_SIGN_TYPES,
            primary_type="HyperliquidTransaction:ApproveBuilderFee",
            is_mainnet=is_mainnet,
        )

    def sign_perp_dex_class_transfer_action(
        self,
        dex: str,
        token: str,
        amount: Union[str, float],
        to_perp: bool,
        transfer_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a perpetual DEX class transfer action.

        Args:
            dex: The DEX identifier.
            token: The token identifier.
            amount: The amount to transfer as a string or float.
            to_perp: Whether transferring to perpetual.
            transfer_params: Optional dictionary with additional parameters:
                - nonce: The nonce (if None, current time will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).

        Returns:
            A dictionary with the signature and payload ready to be sent to the API.
        """
        # Default parameters
        if transfer_params is None:
            transfer_params = {}

        is_mainnet = transfer_params.get("is_mainnet", self._is_mainnet)
        nonce = transfer_params.get("nonce", None)

        # Generate timestamp if not provided
        if nonce is None:
            nonce = self._clock.timestamp_ms()

        # Convert amount to wire format if it's a float
        amount_str = float_to_wire(amount)

        action = {
            "dex": dex,
            "token": token,
            "amount": amount_str,
            "toPerp": to_perp,
            "nonce": nonce,
        }

        return self.sign_user_signed_action(
            action=action,
            payload_types=PERP_DEX_CLASS_TRANSFER_SIGN_TYPES,
            primary_type="HyperliquidTransaction:PerpDexClassTransfer",
            is_mainnet=is_mainnet,
        )

    def sign_token_delegate_action(
        self,
        validator: str,
        wei: int,
        is_undelegate: bool,
        delegate_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a token delegate action.

        Args:
            validator: The validator address.
            wei: The amount in wei.
            is_undelegate: Whether this is an undelegate operation.
            delegate_params: Optional dictionary with additional parameters:
                - nonce: The nonce (if None, current time will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).

        Returns:
            A dictionary with the signature and payload ready to be sent to the API.
        """
        # Default parameters
        if delegate_params is None:
            delegate_params = {}

        is_mainnet = delegate_params.get("is_mainnet", self._is_mainnet)
        nonce = delegate_params.get("nonce", None)

        # Generate timestamp if not provided
        if nonce is None:
            nonce = self._clock.timestamp_ms()

        action = {
            "validator": validator,
            "wei": wei,
            "isUndelegate": is_undelegate,
            "nonce": nonce,
        }

        return self.sign_user_signed_action(
            action=action,
            payload_types=TOKEN_DELEGATE_TYPES,
            primary_type="HyperliquidTransaction:TokenDelegate",
            is_mainnet=is_mainnet,
        )

    def sign_convert_to_multi_sig_user_action(
        self,
        signers: str,
        convert_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a convert to multi-sig user action.

        Args:
            signers: The signers string.
            convert_params: Optional dictionary with additional parameters:
                - nonce: The nonce (if None, current time will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).

        Returns:
            A dictionary with the signature and payload ready to be sent to the API.
        """
        # Default parameters
        if convert_params is None:
            convert_params = {}

        is_mainnet = convert_params.get("is_mainnet", self._is_mainnet)
        nonce = convert_params.get("nonce", None)

        # Generate timestamp if not provided
        if nonce is None:
            nonce = self._clock.timestamp_ms()

        action = {
            "signers": signers,
            "nonce": nonce,
        }

        return self.sign_user_signed_action(
            action=action,
            payload_types=CONVERT_TO_MULTI_SIG_USER_SIGN_TYPES,
            primary_type="HyperliquidTransaction:ConvertToMultiSigUser",
            is_mainnet=is_mainnet,
        )

    def sign_approve_agent(
        self,
        agent_address: str,
        agent_name: str,
        agent_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs an approve agent action.

        Args:
            agent_address: The agent address.
            agent_name: The agent name.
            agent_params: Optional dictionary with additional parameters:
                - nonce: The nonce (if None, current time will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).

        Returns:
            A dictionary with the signature and payload ready to be sent to the API.
        """
        # Default parameters
        if agent_params is None:
            agent_params = {}

        is_mainnet = agent_params.get("is_mainnet", self._is_mainnet)
        nonce = agent_params.get("nonce", None)

        # Generate timestamp if not provided
        if nonce is None:
            nonce = self._clock.timestamp_ms()

        action = {
            "agentAddress": agent_address,
            "agentName": agent_name,
            "nonce": nonce,
        }

        return self.sign_user_signed_action(
            action=action,
            payload_types=APPROVE_AGENT_SIGN_TYPES,
            primary_type="HyperliquidTransaction:ApproveAgent",
            is_mainnet=is_mainnet,
        )

    @staticmethod
    def add_multi_sig_types(sign_types: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        Adds multi-signature fields to sign types.

        Args:
            sign_types: The original sign types.

        Returns:
            Enhanced sign types with multi-sig fields.
        """
        enriched_sign_types = []
        enriched = False
        for sign_type in sign_types:
            enriched_sign_types.append(sign_type)
            if sign_type["name"] == "hyperliquidChain":
                enriched = True
                enriched_sign_types.append({
                    "name": "payloadMultiSigUser",
                    "type": "address",
                })
                enriched_sign_types.append({
                    "name": "outerSigner",
                    "type": "address",
                })
        if not enriched:
            print('"hyperliquidChain" missing from sign_types. sign_types was not enriched with multi-sig signing types')
        return enriched_sign_types

    @staticmethod
    def add_multi_sig_fields(action: Dict[str, Any], payload_multi_sig_user: str, outer_signer: str) -> Dict[str, Any]:
        """
        Adds multi-signature fields to an action.

        Args:
            action: The original action.
            payload_multi_sig_user: The payload multi-sig user address.
            outer_signer: The outer signer address.

        Returns:
            Action with multi-sig fields added.
        """
        action = action.copy()
        action["payloadMultiSigUser"] = payload_multi_sig_user.lower()
        action["outerSigner"] = outer_signer.lower()
        return action

    def sign_multi_sig_user_signed_action_payload(
        self,
        action: Dict[str, Any],
        sign_types: List[Dict[str, str]],
        tx_type: str,
        payload_multi_sig_user: str,
        outer_signer: str,
        multi_sig_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a multi-signature user-signed action payload.

        Args:
            action: The action to sign.
            sign_types: The sign types for the action.
            tx_type: The transaction type.
            payload_multi_sig_user: The payload multi-sig user address.
            outer_signer: The outer signer address.
            multi_sig_params: Optional dictionary with additional parameters:
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).

        Returns:
            A dictionary with the signature and payload ready to be sent to the API.
        """
        # Default parameters
        if multi_sig_params is None:
            multi_sig_params = {}

        is_mainnet = multi_sig_params.get("is_mainnet", self._is_mainnet)

        envelope = self.add_multi_sig_fields(action, payload_multi_sig_user, outer_signer)
        enhanced_sign_types = self.add_multi_sig_types(sign_types)

        return self.sign_user_signed_action(
            action=envelope,
            payload_types=enhanced_sign_types,
            primary_type=tx_type,
            is_mainnet=is_mainnet,
        )

    def sign_multi_sig_l1_action_payload(
        self,
        action: Dict[str, Any],
        payload_multi_sig_user: str,
        outer_signer: str,
        multi_sig_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a multi-signature L1 action payload.

        Args:
            action: The action to sign.
            payload_multi_sig_user: The payload multi-sig user address.
            outer_signer: The outer signer address.
            multi_sig_params: Optional dictionary with additional parameters:
                - vault_address: Optional vault address.
                - nonce: The nonce value (if None, current timestamp will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).
                - expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            A dictionary with the signature and payload ready to be sent to the API.
        """
        # Default parameters
        if multi_sig_params is None:
            multi_sig_params = {}

        vault_address = multi_sig_params.get("vault_address", self._vault_address)
        nonce = multi_sig_params.get("nonce", None)
        is_mainnet = multi_sig_params.get("is_mainnet", self._is_mainnet)
        expires_after = multi_sig_params.get("expires_after", None)

        # Generate timestamp if not provided
        if nonce is None:
            nonce = self._clock.timestamp_ms()

        envelope = [payload_multi_sig_user.lower(), outer_signer.lower(), action]

        return self.sign_l1_action(
            action=envelope,
            vault_address=vault_address,
            nonce=nonce,
            is_mainnet=is_mainnet,
            expires_after=expires_after,
        )

    def sign_multi_sig_action(
        self,
        action: Dict[str, Any],
        multi_sig_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a multi-signature action.

        Args:
            action: The action to sign.
            multi_sig_params: Optional dictionary with additional parameters:
                - vault_address: Optional vault address.
                - nonce: The nonce value (if None, current timestamp will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).
                - expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            A dictionary with the signature and payload ready to be sent to the API.
        """
        # Default parameters
        if multi_sig_params is None:
            multi_sig_params = {}

        vault_address = multi_sig_params.get("vault_address", self._vault_address)
        nonce = multi_sig_params.get("nonce", None)
        is_mainnet = multi_sig_params.get("is_mainnet", self._is_mainnet)
        expires_after = multi_sig_params.get("expires_after", None)

        # Generate timestamp if not provided
        if nonce is None:
            nonce = self._clock.timestamp_ms()

        action_without_tag = action.copy()
        if "type" in action_without_tag:
            del action_without_tag["type"]

        multi_sig_action_hash = self.action_hash(action_without_tag, vault_address, nonce, expires_after)
        envelope = {
            "multiSigActionHash": multi_sig_action_hash,
            "nonce": nonce,
        }

        return self.sign_user_signed_action(
            action=envelope,
            payload_types=MULTI_SIG_ENVELOPE_SIGN_TYPES,
            primary_type="HyperliquidTransaction:SendMultiSig",
            is_mainnet=is_mainnet,
        )

# class NonceManager:
#     """
#     Manages nonces for API requests, ensuring they are unique and time-based.

#     Provides thread-safe nonce generation that follows the official SDK pattern
#     but adds collision prevention for concurrent requests. Like the official SDK,
#     this uses timestamps as nonces but ensures uniqueness when multiple requests
#     happen in the same millisecond.
#     """
#     def __init__(self, clock: Optional[LiveClock] = None):
#         """
#         Initialize a new nonce manager.

#         Args:
#             clock: Optional LiveClock for timestamp generation. If None, system time will be used.
#         """
#         self._clock = clock
#         self._last_nonce: int = self._current_timestamp_ms()
#         self._lock = threading.Lock()

#     def _current_timestamp_ms(self) -> int:
#         """Returns the current time in milliseconds since the UNIX epoch."""
#         if self._clock:
#             return self._clock.timestamp_ms()
#         else:
#             import time
#             return int(time.time() * 1000)

#     def get_next_nonce(self) -> int:
#         """
#         Generates the next nonce following the official SDK pattern.

#         Uses current timestamp as nonce (like official SDK) but ensures uniqueness
#         for concurrent requests by incrementing if needed.

#         Returns:
#             The next nonce value (timestamp-based).
#         """
#         with self._lock:
#             current_time_ms = self._current_timestamp_ms()
#             # Ensure nonce is at least current time and greater than the last one
#             # This matches official SDK behavior but prevents collisions
#             self._last_nonce = max(self._last_nonce + 1, current_time_ms)
#             return self._last_nonce

#     # Note: Removed get_timestamp_nonce() - use get_next_nonce() directly


