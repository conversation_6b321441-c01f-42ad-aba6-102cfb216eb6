import pytest
from unittest.mock import Mock
from eth_account import Account
from eth_utils import to_hex

from signing import (
    HyperliquidSigner,
    float_to_wire,
    float_to_int_for_hashing,
    float_to_int,
    L1_AGENT_EIP712_DOMAIN,
    L1_AGENT_MESSAGE_TYPE,
    USD_SEND_SIGN_TYPES,
    WITHDRAW_SIGN_TYPES,
    APPROVE_BUILDER_FEE_SIGN_TYPES,
)
from nautilus_trader.common.component import LiveClock


class TestFloatToWire:
    """Test the float_to_wire utility function."""

    def test_float_to_wire_string_passthrough(self):
        """Test that string inputs are passed through unchanged."""
        assert float_to_wire("1.********") == "1.********"
        assert float_to_wire("0") == "0"
        assert float_to_wire("1000") == "1000"

    def test_float_to_wire_float_conversion(self):
        """Test that float inputs are converted properly."""
        assert float_to_wire(1.0) == "1"
        assert float_to_wire(0.0) == "0"
        assert float_to_wire(1.23) == "1.23"
        assert float_to_wire(0.********) == "0.********"

    def test_float_to_wire_negative_zero(self):
        """Test that -0 is converted to 0."""
        assert float_to_wire(-0) == "0"

    def test_float_to_wire_precision_error(self):
        """Test that precision errors raise ValueError."""
        with pytest.raises(ValueError, match="float_to_wire causes rounding"):
            # This should cause rounding error due to float precision
            float_to_wire(1.1********1********)


class TestFloatToIntForHashing:
    """Test the float_to_int_for_hashing utility function (similar to signing_test.py)."""

    def test_float_to_int_for_hashing_large_numbers(self):
        """Test float to int conversion for large numbers."""
        assert float_to_int_for_hashing(123123123123) == 12312312312300000000

    def test_float_to_int_for_hashing_small_numbers(self):
        """Test float to int conversion for small numbers."""
        assert float_to_int_for_hashing(0.********) == 1231

    def test_float_to_int_for_hashing_decimal_numbers(self):
        """Test float to int conversion for decimal numbers."""
        assert float_to_int_for_hashing(1.033) == 103300000

    def test_float_to_int_for_hashing_precision_error(self):
        """Test that precision errors raise ValueError."""
        with pytest.raises(ValueError):
            float_to_int_for_hashing(0.********2312)


class TestHyperliquidSigner:
    """Test the HyperliquidSigner class."""

    @pytest.fixture
    def mock_clock(self):
        """Create a mock clock."""
        clock = Mock(spec=LiveClock)
        clock.timestamp_ms.return_value = 1677777606040
        return clock

    @pytest.fixture
    def test_private_key(self):
        """Test private key for consistent testing."""
        return "0x01********01********01********01********01********01********0123"

    @pytest.fixture
    def signer_mainnet(self, mock_clock, test_private_key):
        """Create a mainnet signer for testing."""
        return HyperliquidSigner(
            clock=mock_clock,
            private_key_hex=test_private_key,
            is_mainnet=True
        )

    @pytest.fixture
    def signer_testnet(self, mock_clock, test_private_key):
        """Create a testnet signer for testing."""
        return HyperliquidSigner(
            clock=mock_clock,
            private_key_hex=test_private_key,
            is_mainnet=False
        )

    def test_signer_initialization_with_0x_prefix(self, mock_clock):
        """Test signer initialization with 0x prefix."""
        private_key = "0x01********01********01********01********01********01********0123"
        signer = HyperliquidSigner(clock=mock_clock, private_key_hex=private_key)
        assert signer._account.address is not None
        assert signer._is_mainnet is True  # Default

    def test_signer_initialization_without_0x_prefix(self, mock_clock):
        """Test signer initialization without 0x prefix."""
        private_key = "01********01********01********01********01********01********0123"
        signer = HyperliquidSigner(clock=mock_clock, private_key_hex=private_key)
        assert signer._account.address is not None

    def test_signer_initialization_with_invalid_key(self, mock_clock):
        """Test signer initialization with invalid private key."""
        with pytest.raises(ValueError, match="Invalid private key"):
            HyperliquidSigner(clock=mock_clock, private_key_hex="invalid_key")

    def test_address_to_bytes(self):
        """Test address to bytes conversion."""
        address = "0x1719884eb866cb12b2287399b15f7db5e7d775ea"
        result = HyperliquidSigner.address_to_bytes(address)
        expected = bytes.fromhex("1719884eb866cb12b2287399b15f7db5e7d775ea")
        assert result == expected

    def test_address_to_bytes_without_0x(self):
        """Test address to bytes conversion without 0x prefix."""
        address = "1719884eb866cb12b2287399b15f7db5e7d775ea"
        result = HyperliquidSigner.address_to_bytes(address)
        expected = bytes.fromhex("1719884eb866cb12b2287399b15f7db5e7d775ea")
        assert result == expected

    def test_action_hash_without_vault(self, signer_mainnet):
        """Test action hash computation without vault address."""
        action = {"type": "dummy", "num": 1000}
        nonce = 0
        hash_result = signer_mainnet.action_hash(action, None, nonce)
        assert isinstance(hash_result, bytes)
        assert len(hash_result) == 32  # keccak256 produces 32 bytes

    def test_action_hash_with_vault(self, signer_mainnet):
        """Test action hash computation with vault address."""
        action = {"type": "dummy", "num": 1000}
        vault_address = "0x1719884eb866cb12b2287399b15f7db5e7d775ea"
        nonce = 0
        hash_result = signer_mainnet.action_hash(action, vault_address, nonce)
        assert isinstance(hash_result, bytes)
        assert len(hash_result) == 32

    def test_construct_phantom_agent_mainnet(self, signer_mainnet):
        """Test phantom agent construction for mainnet."""
        hash_bytes = b'\x0f\xcb\xed\xa5\xae<IP\xa5H\x02\x15R\xa4\xfe\xa2"hX\xc4E5q\xbf?$\xba\x01~\xac)\x08'
        phantom_agent = signer_mainnet.construct_phantom_agent(hash_bytes, True)
        assert phantom_agent["source"] == "a"
        assert phantom_agent["connectionId"] == hash_bytes

    def test_construct_phantom_agent_testnet(self, signer_testnet):
        """Test phantom agent construction for testnet."""
        hash_bytes = b'\x0f\xcb\xed\xa5\xae<IP\xa5H\x02\x15R\xa4\xfe\xa2"hX\xc4E5q\xbf?$\xba\x01~\xac)\x08'
        phantom_agent = signer_testnet.construct_phantom_agent(hash_bytes, False)
        assert phantom_agent["source"] == "b"
        assert phantom_agent["connectionId"] == hash_bytes

    def test_phantom_agent_creation_structure(self, mock_clock, test_private_key):
        """Test phantom agent creation structure (similar to signing_test.py)."""
        # Create signer with specific timestamp
        clock = Mock(spec=LiveClock)
        clock.timestamp_ms.return_value = 1677777606040
        signer = HyperliquidSigner(
            clock=clock,
            private_key_hex=test_private_key,
            is_mainnet=True
        )

        # Create order similar to signing_test.py
        order_params = {
            "asset": 4,  # ETH asset ID from signing_test.py
            "is_buy": True,
            "sz": 0.0147,
            "limit_px": 1670.1,
            "order_type": {"limit": {"tif": "Ioc"}},
            "reduce_only": False,
        }

        # This should produce a valid hash
        action = {"type": "order", "orders": [order_params], "grouping": "na"}
        hash_result = signer.action_hash(action, None, 1677777606040)
        phantom_agent = signer.construct_phantom_agent(hash_result, True)

        # Verify phantom agent structure
        assert "source" in phantom_agent
        assert "connectionId" in phantom_agent
        assert phantom_agent["source"] == "a"  # mainnet uses "a"
        assert isinstance(phantom_agent["connectionId"], bytes)
        assert len(phantom_agent["connectionId"]) == 32  # 32 bytes for hash

    def test_sign_l1_action_basic_mainnet(self, signer_mainnet):
        """Test basic L1 action signing for mainnet with expected values."""
        action = {"type": "dummy", "num": float_to_int_for_hashing(1000)}
        signature = signer_mainnet.sign_l1_action(action, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0x53749d5b30552aeb2fca34b530185976545bb22d0b3ce6f62e31be961a59298"
        assert signature["s"] == "0x755c40ba9bf05223521753995abb2f73ab3229be8ec921f350cb447e384d8ed8"
        assert signature["v"] == 27

    def test_sign_l1_action_basic_testnet(self, signer_testnet):
        """Test basic L1 action signing for testnet with expected values."""
        action = {"type": "dummy", "num": float_to_int_for_hashing(1000)}
        signature = signer_testnet.sign_l1_action(action, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0x542af61ef1f429707e3c76c5293c80d01f74ef853e34b76efffcb57e574f9510"
        assert signature["s"] == "0x17b8b32f086e8cdede991f1e2c529f5dd5297cbe8128500e00cbaf766204a613"
        assert signature["v"] == 28

    def test_sign_l1_action_with_vault_mainnet(self, signer_mainnet):
        """Test L1 action signing with vault address for mainnet with expected values."""
        action = {"type": "dummy", "num": float_to_int_for_hashing(1000)}
        vault_address = "0x1719884eb866cb12b2287399b15f7db5e7d775ea"
        signature = signer_mainnet.sign_l1_action(action, vault_address=vault_address, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0x3c548db75e479f8012acf3000ca3a6b05606bc2ec0c29c50c515066a326239"
        assert signature["s"] == "0x4d402be7396ce74fbba3795769cda45aec00dc3125a984f2a9f23177b190da2c"
        assert signature["v"] == 28

    def test_sign_l1_action_with_vault_testnet(self, signer_testnet):
        """Test L1 action signing with vault address for testnet with expected values."""
        action = {"type": "dummy", "num": float_to_int_for_hashing(1000)}
        vault_address = "0x1719884eb866cb12b2287399b15f7db5e7d775ea"
        signature = signer_testnet.sign_l1_action(action, vault_address=vault_address, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0xe281d2fb5c6e25ca01601f878e4d69c965bb598b88fac58e475dd1f5e56c362b"
        assert signature["s"] == "0x7ddad27e9a238d045c035bc606349d075d5c5cd00a6cd1da23ab5c39d4ef0f60"
        assert signature["v"] == 27

    def test_sign_order_basic_mainnet(self, signer_mainnet):
        """Test basic order signing for mainnet (structure validation)."""
        order_params = {
            "asset": 1,  # ETH asset ID
            "is_buy": True,
            "sz": 100,
            "limit_px": 100,
            "order_type": {"limit": {"tif": "Gtc"}},
            "reduce_only": False,
            "nonce": 0,  # Use timestamp 0 for consistency
        }

        signature = signer_mainnet.sign_order(order_params)

        # Verify signature structure and validity (similar to signing_test.py approach)
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature
        assert signature["r"].startswith("0x")
        assert signature["s"].startswith("0x")
        # Hyperliquid mainnet uses v = 27 for trigger orders (from signing_test.py)
        assert signature["v"] == 27
        assert len(signature["r"]) == 66  # 0x + 64 hex chars
        assert len(signature["s"]) == 66  # 0x + 64 hex chars

    def test_sign_order_basic_testnet(self, signer_testnet):
        """Test basic order signing for testnet (structure validation)."""
        order_params = {
            "asset": 1,  # ETH asset ID
            "is_buy": True,
            "sz": 100,
            "limit_px": 100,
            "order_type": {"limit": {"tif": "Gtc"}},
            "reduce_only": False,
            "nonce": 0,  # Use timestamp 0 for consistency
        }

        signature = signer_testnet.sign_order(order_params)

        # Verify signature structure and validity (similar to signing_test.py approach)
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature
        assert signature["r"].startswith("0x")
        assert signature["s"].startswith("0x")
        # Hyperliquid testnet uses v = 28 for trigger orders (from signing_test.py)
        assert signature["v"] == 28
        assert len(signature["r"]) == 66  # 0x + 64 hex chars
        assert len(signature["s"]) == 66  # 0x + 64 hex chars

    def test_sign_order_with_cloid_mainnet(self, signer_mainnet):
        """Test order signing with client order ID for mainnet (structure validation)."""
        order_params = {
            "asset": 1,  # ETH asset ID
            "is_buy": True,
            "sz": 100,
            "limit_px": 100,
            "order_type": {"limit": {"tif": "Gtc"}},
            "reduce_only": False,
            "cloid": "0x00000000000000000000000000000001",
            "nonce": 0,  # Use timestamp 0 for consistency
        }

        signature = signer_mainnet.sign_order(order_params)

        # Verify signature structure and validity (similar to signing_test.py approach)
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature
        assert signature["r"].startswith("0x")
        assert signature["s"].startswith("0x")
        # Hyperliquid mainnet uses v = 27 for order with cloid (from signing_test.py)
        assert signature["v"] == 27
        assert len(signature["r"]) == 66  # 0x + 64 hex chars
        assert len(signature["s"]) == 66  # 0x + 64 hex chars

    def test_sign_order_with_cloid_testnet(self, signer_testnet):
        """Test order signing with client order ID for testnet (structure validation)."""
        order_params = {
            "asset": 1,  # ETH asset ID
            "is_buy": True,
            "sz": 100,
            "limit_px": 100,
            "order_type": {"limit": {"tif": "Gtc"}},
            "reduce_only": False,
            "cloid": "0x00000000000000000000000000000001",
            "nonce": 0,  # Use timestamp 0 for consistency
        }

        signature = signer_testnet.sign_order(order_params)

        # Verify signature structure and validity (similar to signing_test.py approach)
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature
        assert signature["r"].startswith("0x")
        assert signature["s"].startswith("0x")
        # Hyperliquid testnet uses v = 28 for order with cloid (from signing_test.py)
        assert signature["v"] == 28
        assert len(signature["r"]) == 66  # 0x + 64 hex chars
        assert len(signature["s"]) == 66  # 0x + 64 hex chars

    def test_sign_order_trigger_type_mainnet(self, signer_mainnet):
        """Test order signing with trigger order type for mainnet (structure validation)."""
        order_params = {
            "asset": 1,  # ETH asset ID
            "is_buy": True,
            "sz": 100,
            "limit_px": 100,
            "order_type": {"trigger": {"triggerPx": 103, "isMarket": True, "tpsl": "sl"}},
            "reduce_only": False,
            "nonce": 0,  # Use timestamp 0 for consistency
        }

        signature = signer_mainnet.sign_order(order_params)

        # Verify signature structure and validity (similar to signing_test.py approach)
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature
        assert signature["r"].startswith("0x")
        assert signature["s"].startswith("0x")
        # Hyperliquid mainnet consistently uses v = 27
        assert signature["v"] == 27
        assert len(signature["r"]) == 66  # 0x + 64 hex chars
        assert len(signature["s"]) == 66  # 0x + 64 hex chars

    def test_sign_order_trigger_type_testnet(self, signer_testnet):
        """Test order signing with trigger order type for testnet (structure validation)."""
        order_params = {
            "asset": 1,  # ETH asset ID
            "is_buy": True,
            "sz": 100,
            "limit_px": 100,
            "order_type": {"trigger": {"triggerPx": 103, "isMarket": True, "tpsl": "sl"}},
            "reduce_only": False,
            "nonce": 0,  # Use timestamp 0 for consistency
        }

        signature = signer_testnet.sign_order(order_params)

        # Verify signature structure and validity (similar to signing_test.py approach)
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature
        assert signature["r"].startswith("0x")
        assert signature["s"].startswith("0x")
        # Hyperliquid testnet consistently uses v = 28
        assert signature["v"] == 28
        assert len(signature["r"]) == 66  # 0x + 64 hex chars
        assert len(signature["s"]) == 66  # 0x + 64 hex chars

    def test_order_type_to_wire_limit(self, signer_mainnet):
        """Test order type to wire conversion for limit orders."""
        order_type = {"limit": {"tif": "Gtc"}}
        wire_format = signer_mainnet._order_type_to_wire(order_type)
        assert wire_format == {"limit": {"tif": "Gtc"}}

    def test_order_type_to_wire_trigger(self, signer_mainnet):
        """Test order type to wire conversion for trigger orders."""
        order_type = {"trigger": {"triggerPx": 30000.0, "isMarket": True, "tpsl": "tp"}}
        wire_format = signer_mainnet._order_type_to_wire(order_type)
        expected = {
            "trigger": {
                "isMarket": True,
                "triggerPx": "30000",
                "tpsl": "tp",
            }
        }
        assert wire_format == expected

    def test_order_type_to_wire_invalid(self, signer_mainnet):
        """Test order type to wire conversion with invalid order type."""
        order_type = {"invalid": {"some": "data"}}
        with pytest.raises(ValueError, match="Invalid order type"):
            signer_mainnet._order_type_to_wire(order_type)

    def test_sign_cancel_order(self, signer_mainnet):
        """Test cancel order signing."""
        client_id = "test_order_123"
        signature = signer_mainnet.sign_cancel_order(client_id)

        # Verify signature structure
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_sign_update_leverage(self, signer_mainnet):
        """Test update leverage signing."""
        asset_id = 0
        leverage = 10.0
        signature = signer_mainnet.sign_update_leverage(asset_id, leverage)

        # Verify signature structure
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_sign_withdraw(self, signer_mainnet):
        """Test withdraw signing (structure validation)."""
        amount = 1.0  # Use same amount as signing_test.py
        destination = "0x5e9ee1089755c3435139848e47e6635505d5a13a"

        # Create withdraw params with specific time to match signing_test.py
        withdraw_params = {"time_ms": 1687816341423}
        signature = signer_mainnet.sign_withdraw(amount, destination, withdraw_params)

        # Verify signature structure and validity (similar to signing_test.py approach)
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature
        assert signature["r"].startswith("0x")
        assert signature["s"].startswith("0x")
        # Hyperliquid mainnet uses v = 28 for withdraw actions (from signing_test.py)
        assert signature["v"] == 28
        assert len(signature["r"]) == 66  # 0x + 64 hex chars
        assert len(signature["s"]) == 66  # 0x + 64 hex chars

    def test_sign_usd_transfer_action_mainnet(self, signer_mainnet):
        """Test USD transfer action signing for mainnet (structure validation)."""
        destination = "0x5e9ee1089755c3435139848e47e6635505d5a13a"
        amount = 1.0
        transfer_params = {"time_ms": 1687816341423}

        signature = signer_mainnet.sign_usd_transfer_action(destination, amount, transfer_params)

        # Verify signature structure and validity (similar to signing_test.py approach)
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature
        assert signature["r"].startswith("0x")
        assert signature["s"].startswith("0x")
        assert signature["v"] in [27, 28]
        assert len(signature["r"]) == 66  # 0x + 64 hex chars
        assert len(signature["s"]) == 66  # 0x + 64 hex chars

    def test_sign_usd_transfer_action_testnet(self, signer_testnet):
        """Test USD transfer action signing for testnet."""
        destination = "0x5e9ee1089755c3435139848e47e6635505d5a13a"
        amount = 1.0
        transfer_params = {"time_ms": 1687816341423}

        signature = signer_testnet.sign_usd_transfer_action(destination, amount, transfer_params)

        # Verify signature structure (testnet values would be different)
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_sign_approve_builder_fee(self, signer_mainnet):
        """Test approve builder fee signing."""
        max_fee_rate = "0.0001"
        builder = "0x1********01********01********01********0"

        signature = signer_mainnet.sign_approve_builder_fee(max_fee_rate, builder)

        # Verify signature structure
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_sign_perp_dex_class_transfer_action(self, signer_mainnet):
        """Test perpetual DEX class transfer action signing."""
        dex = "hyperliquid"
        token = "USDC"
        amount = 1000.0
        to_perp = True

        signature = signer_mainnet.sign_perp_dex_class_transfer_action(dex, token, amount, to_perp)

        # Verify signature structure
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_sign_token_delegate_action(self, signer_mainnet):
        """Test token delegate action signing."""
        validator = "0x1********01********01********01********0"
        wei = 1000000000000000000  # 1 ETH in wei
        is_undelegate = False

        signature = signer_mainnet.sign_token_delegate_action(validator, wei, is_undelegate)

        # Verify signature structure
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_sign_convert_to_multi_sig_user_action(self, signer_mainnet):
        """Test convert to multi-sig user action signing."""
        signers = "0x1********01********01********01********0,******************************************"

        signature = signer_mainnet.sign_convert_to_multi_sig_user_action(signers)

        # Verify signature structure
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_sign_approve_agent(self, signer_mainnet):
        """Test approve agent signing."""
        agent_address = "0x1********01********01********01********0"
        agent_name = "test_agent"

        signature = signer_mainnet.sign_approve_agent(agent_address, agent_name)

        # Verify signature structure
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_sign_user_signed_action_basic(self, signer_mainnet):
        """Test basic user signed action."""
        action = {
            "destination": "0x5e9ee1089755c3435139848e47e6635505d5a13a",
            "amount": "1",
            "time": 1687816341423,
        }

        signature = signer_mainnet.sign_user_signed_action(
            action=action,
            payload_types=USD_SEND_SIGN_TYPES,
            primary_type="HyperliquidTransaction:UsdSend"
        )

        # Verify signature structure
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_signer_with_vault_address_default(self, mock_clock, test_private_key):
        """Test signer with default vault address."""
        vault_address = "0x1719884eb866cb12b2287399b15f7db5e7d775ea"
        signer = HyperliquidSigner(
            clock=mock_clock,
            private_key_hex=test_private_key,
            vault_address=vault_address
        )

        assert signer._vault_address == vault_address

        # Test that vault address is used by default in L1 actions
        action = {"type": "dummy", "num": 1000}
        signature = signer.sign_l1_action(action, nonce=0)

        # Should not raise any errors and produce valid signature
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_automatic_nonce_generation(self, signer_mainnet):
        """Test that nonce is automatically generated when not provided."""
        action = {"type": "dummy", "num": 1000}

        # Don't provide nonce - should use clock timestamp
        signature = signer_mainnet.sign_l1_action(action)

        # Should not raise any errors and produce valid signature
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_automatic_time_generation_usd_transfer(self, signer_mainnet):
        """Test that time is automatically generated for USD transfer when not provided."""
        destination = "0x5e9ee1089755c3435139848e47e6635505d5a13a"
        amount = 1.0

        # Don't provide time_ms - should use clock timestamp
        signature = signer_mainnet.sign_usd_transfer_action(destination, amount)

        # Should not raise any errors and produce valid signature
        assert "r" in signature
        assert "s" in signature
        assert "v" in signature

    def test_create_sub_account_action_mainnet(self, signer_mainnet):
        """Test create sub account action signing for mainnet with expected values."""
        action = {
            "type": "createSubAccount",
            "name": "example",
        }
        signature = signer_mainnet.sign_l1_action(action, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0x51096fe3239421d16b671e192f574ae24ae14329099b6db28e479b86cdd6caa7"
        assert signature["s"] == "0xb71f7d293af92d3772572afb8b102d167a7cef7473388286bc01f52a5c5b423"
        assert signature["v"] == 27

    def test_create_sub_account_action_testnet(self, signer_testnet):
        """Test create sub account action signing for testnet with expected values."""
        action = {
            "type": "createSubAccount",
            "name": "example",
        }
        signature = signer_testnet.sign_l1_action(action, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0xa699e3ed5c2b89628c746d3298b5dc1cca604694c2c855da8bb8250ec8014a5b"
        assert signature["s"] == "0x53f1b8153a301c72ecc655b1c315d64e1dcea3ee58921fd7507e35818fcc1584"
        assert signature["v"] == 28

    def test_sub_account_transfer_action_mainnet(self, signer_mainnet):
        """Test sub account transfer action signing for mainnet with expected values."""
        action = {
            "type": "subAccountTransfer",
            "subAccountUser": "0x1d9470d4b963f552e6f671a81619d395877bf409",
            "isDeposit": True,
            "usd": 10,
        }
        signature = signer_mainnet.sign_l1_action(action, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0x43592d7c6c7d816ece2e206f174be61249d651944932b13343f4d13f306ae602"
        assert signature["s"] == "0x71a926cb5c9a7c01c3359ec4c4c34c16ff8107d610994d4de0e6430e5cc0f4c9"
        assert signature["v"] == 28

    def test_sub_account_transfer_action_testnet(self, signer_testnet):
        """Test sub account transfer action signing for testnet with expected values."""
        action = {
            "type": "subAccountTransfer",
            "subAccountUser": "0x1d9470d4b963f552e6f671a81619d395877bf409",
            "isDeposit": True,
            "usd": 10,
        }
        signature = signer_testnet.sign_l1_action(action, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0xe26574013395ad55ee2f4e0575310f003c5bb3351b5425482e2969fa51543927"
        assert signature["s"] == "0xefb08999196366871f919fd0e138b3a7f30ee33e678df7cfaf203e25f0a4278"
        assert signature["v"] == 28

    def test_schedule_cancel_action_mainnet(self, signer_mainnet):
        """Test schedule cancel action signing for mainnet with expected values."""
        action = {
            "type": "scheduleCancel",
        }
        signature = signer_mainnet.sign_l1_action(action, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0x6cdfb286702f5917e76cd9b3b8bf678fcc49aec194c02a73e6d4f16891195df9"
        assert signature["s"] == "0x6557ac307fa05d25b8d61f21fb8a938e703b3d9bf575f6717ba21ec61261b2a0"
        assert signature["v"] == 27

    def test_schedule_cancel_action_testnet(self, signer_testnet):
        """Test schedule cancel action signing for testnet with expected values."""
        action = {
            "type": "scheduleCancel",
        }
        signature = signer_testnet.sign_l1_action(action, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0xc75bb195c3f6a4e06b7d395acc20bbb224f6d23ccff7c6a26d327304e6efaeed"
        assert signature["s"] == "0x342f8ede109a29f2c0723bd5efb9e9100e3bbb493f8fb5164ee3d385908233df"
        assert signature["v"] == 28

    def test_schedule_cancel_action_with_time_mainnet(self, signer_mainnet):
        """Test schedule cancel action with time signing for mainnet with expected values."""
        action = {
            "type": "scheduleCancel",
            "time": 1********,
        }
        signature = signer_mainnet.sign_l1_action(action, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0x609cb20c737945d070716dcc696ba030e9976fcf5edad87afa7d877493109d55"
        assert signature["s"] == "0x16c685d63b5c7a04512d73f183b3d7a00da5406ff1f8aad33f8ae2163bab758b"
        assert signature["v"] == 28

    def test_schedule_cancel_action_with_time_testnet(self, signer_testnet):
        """Test schedule cancel action with time signing for testnet with expected values."""
        action = {
            "type": "scheduleCancel",
            "time": 1********,
        }
        signature = signer_testnet.sign_l1_action(action, nonce=0)

        # Test against expected signature values (similar to signing_test.py)
        assert signature["r"] == "0x4e4f2dbd4107c69783e251b7e1057d9f2b9d11cee213441ccfa2be63516dc5bc"
        assert signature["s"] == "0x706c656b23428c8ba356d68db207e11139ede1670481a9e01ae2dfcdb0e1a678"
        assert signature["v"] == 27


class TestConstants:
    """Test the signing constants and types."""

    def test_l1_agent_eip712_domain(self):
        """Test L1 agent EIP712 domain structure."""
        assert L1_AGENT_EIP712_DOMAIN["name"] == "Exchange"
        assert L1_AGENT_EIP712_DOMAIN["version"] == "1"
        assert L1_AGENT_EIP712_DOMAIN["chainId"] == 1337
        assert L1_AGENT_EIP712_DOMAIN["verifyingContract"] == "0x0000000000000000000000000000000000000000"

    def test_l1_agent_message_type(self):
        """Test L1 agent message type structure."""
        agent_type = L1_AGENT_MESSAGE_TYPE["Agent"]
        assert len(agent_type) == 2
        assert {"name": "source", "type": "string"} in agent_type
        assert {"name": "connectionId", "type": "bytes32"} in agent_type

    def test_usd_send_sign_types(self):
        """Test USD send signature types."""
        expected_fields = {"hyperliquidChain", "destination", "amount", "time"}
        actual_fields = {field["name"] for field in USD_SEND_SIGN_TYPES}
        assert actual_fields == expected_fields

    def test_withdraw_sign_types(self):
        """Test withdraw signature types."""
        expected_fields = {"hyperliquidChain", "destination", "amount", "time"}
        actual_fields = {field["name"] for field in WITHDRAW_SIGN_TYPES}
        assert actual_fields == expected_fields

    def test_approve_builder_fee_sign_types(self):
        """Test approve builder fee signature types."""
        expected_fields = {"hyperliquidChain", "maxFeeRate", "builder", "nonce"}
        actual_fields = {field["name"] for field in APPROVE_BUILDER_FEE_SIGN_TYPES}
        assert actual_fields == expected_fields
